package com.dong.common.data

import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import java.io.File
import java.util.Objects

class DeviceAppData : Comparable<DeviceAppData> {
    var pkgInfo: PackageInfo? = null
    var alphabetic: String = ""
    var title: String = ""

    constructor(info: PackageInfo?) {
        this.pkgInfo = info
        this.title = pkgInfo?.applicationInfo?.loadLabel(Global.appContext.packageManager)
            .toString()
    }

    constructor(info: PackageInfo, alpha: String) {
        this.pkgInfo = info
        title =
            info.applicationInfo?.loadLabel(Global.appContext.getPackageManager()).toString()
        alphabetic = alpha
    }

    fun abi(info: PackageInfo): String? {
        val dir = info.applicationInfo?.nativeLibraryDir ?: return null
        return File(dir).name
    }

    fun getImageModel(): ApplicationInfo? {
        return pkgInfo?.applicationInfo
    }

    override fun compareTo(o: DeviceAppData): Int {
        return alphabetic.compareTo(o.alphabetic)
    }

    override fun equals(o: Any?): Boolean {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false
        val model: DeviceAppData = o as DeviceAppData
        return pkgInfo?.applicationInfo?.sourceDir == model.pkgInfo?.applicationInfo?.sourceDir && pkgInfo?.lastUpdateTime == model.pkgInfo?.lastUpdateTime && alphabetic == model.alphabetic
    }

    override fun hashCode(): Int {
        return Objects.hash(pkgInfo?.applicationInfo?.sourceDir, pkgInfo?.lastUpdateTime, alphabetic)
    }
}