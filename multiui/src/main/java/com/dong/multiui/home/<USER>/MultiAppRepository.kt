package com.dong.multiui.home.repo

//import com.dong.common.data.dataStore
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import com.dong.common.data.AppConfigData
import com.dong.common.data.Constant
import com.dong.common.data.Global
import com.dong.multiui.home.models.AppData
import com.dong.multiui.home.models.AppInfo
import com.dong.multiui.home.models.AppInfoLite
import com.dong.multiui.home.models.MultiAppData
import com.dong.multiui.home.models.PackageAppData
import com.dong.multiui.util.GmsSupport
import com.dong.multiui.util.InstallResult
import com.dong.multiui.util.InstalledAppInfo
import com.dong.router.MServiceLoader
import com.dong.router.api.IMultiApi
import com.google.gson.Gson
import org.json.JSONArray
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.text.Collator
import java.util.Locale

object MultiAppRepository : AppDataSource {

    var cacheList: List<AppData>? = null
    var cacheDeviceAppList: List<AppInfo>? = null
    val hotAppSet:HashSet<String> =  HashSet()

    init {
        loadHotApps(Global.appContext)
    }

    override fun getVirtualApps(isPrivacySpace: Boolean): ArrayList<AppData> {
        var cacheList:List<AppData>? = getCacheVirtualList()
        cacheList = cacheList?.filter {
            if (isPrivacySpace) {
                it is MultiAppData && it.userId == 0
            } else {
                it is MultiAppData && it.userId != 0
            }
        }
        val configStr = Global.kv().decodeString(if(isPrivacySpace) Constant.Key.KEY_APP_LIST_CONFIG_PRIVACY_SPACE else Constant.Key.KEY_APP_LIST_CONFIG)
        //Log.d("dongj", "AppRepository/getVirtualApps(${Thread.currentThread().name}):isPrivacySpace($isPrivacySpace) configStr($configStr)");
        if (configStr != null) {
            val appConfigMap = HashMap<String, AppConfigData>()
            val configList = Gson().fromJson(configStr, Array<AppConfigData>::class.java)
            for (config in configList) {
                appConfigMap.put("${config.pkgName}_${config.spaceId}", config)
            }
            cacheList?.forEach {
                val appData = it as MultiAppData
                val config = appConfigMap.get("${appData.appInfo.packageName}_${appData.userId}")
                if (config != null) {
                    appData.nickName = config.nickName
                }
            }
            cacheList = cacheList?.sortedBy { appData ->
                val appData = appData as MultiAppData
                val config = appConfigMap.get("${appData.appInfo.packageName}_${appData.userId}")
                if (config != null) {
                    config.position
                } else {
                    cacheList?.size
                }
            }
        }
        return ArrayList(cacheList);
    }

    private fun getCacheVirtualList():List<AppData> {
        if (cacheList == null) {
            val list = MServiceLoader.getService(IMultiApi::class).allAppSpaceList();
            cacheList = list.mapNotNull { appSpaceItem ->
                val pkg = appSpaceItem.pkgInfo ?: return@mapNotNull null
                val installInfo = InstalledAppInfo(
                    pkg,
                    pkg.packageName,
                    pkg.applicationInfo?.sourceDir,
                    pkg.applicationInfo?.nativeLibraryDir,
                    true,
                    false,
                    0
                )
                val pkgData = PackageAppData(Global.appContext, installInfo)
                var mpaData = MultiAppData(
                    pkgData,
                    appSpaceItem.spaceId
                )
                mpaData
            }
        }
        return ArrayList(cacheList)
    }

    fun getAppData(pkgName: String): AppData? {
        return cacheList?.find { (it is MultiAppData) && pkgName.equals(it.appInfo.packageName) }
    }

    override fun getInstalledApps(context: Context): List<AppInfo>? {
        //return VUiKit.defer().when(() -> convertPackageInfoToAppData(context, context.getPackageManager().getInstalledPackages(0), true));
        return null
    }

    override fun getStorageApps(context: Context, rootDir: File): List<AppInfo>? {
        //return VUiKit.defer().when(() -> convertPackageInfoToAppData(context, findAndParseAPKs(context, rootDir, SCAN_PATH_LIST), false));
        return null
    }

    private fun findAndParseAPKs(
        context: Context,
        rootDir: File,
        paths: List<String>?
    ): List<PackageInfo> {
        val packageList: MutableList<PackageInfo> = ArrayList()
        if (paths == null) return packageList
        for (path in paths) {
            val dirFiles = File(rootDir, path).listFiles() ?: continue
            for (f in dirFiles) {
                if (!f.name.lowercase(Locale.getDefault()).endsWith(".apk")) continue
                var pkgInfo: PackageInfo? = null
                try {
                    pkgInfo = context.packageManager.getPackageArchiveInfo(f.absolutePath, 0)
                    pkgInfo!!.applicationInfo?.sourceDir = f.absolutePath
                    pkgInfo.applicationInfo?.publicSourceDir = f.absolutePath
                } catch (e: Exception) {
                    // Ignore
                }
                if (pkgInfo != null) packageList.add(pkgInfo)
            }
        }
        return packageList
    }

    private fun convertPackageInfoToAppData(
        context: Context,
        pkgList: List<PackageInfo>,
        fastOpen: Boolean
    ): List<AppInfo> {
        val pm = context.packageManager
        val list: MutableList<AppInfo> = ArrayList(pkgList.size)
        val hostPkg = ""
        for (pkg in pkgList) {
            // ignore the host package
            if (hostPkg == pkg.packageName) {
                continue
            }
            // ignore the System package
            if (isSystemApplication(pkg)) {
                continue
            }
            val ai = pkg.applicationInfo
            val path = if (ai?.publicSourceDir != null) ai.publicSourceDir else ai?.sourceDir
            if (path == null) {
                continue
            }
            val info = AppInfo()
            info.packageName = pkg.packageName
            info.fastOpen = fastOpen
            info.path = path
            info.icon = ai?.loadIcon(pm)
            info.name = ai?.loadLabel(pm)
            /*InstalledAppInfo installedAppInfo = VirtualCore.get().getInstalledAppInfo(pkg.packageName, 0);
            if (installedAppInfo != null) {
                info.cloneCount = installedAppInfo.getInstalledUsers().length;
            }*/
            list.add(info)
        }
        return list
    }

    override fun addVirtualApp(info: AppInfoLite): InstallResult? {
        /*int flags = InstallStrategy.COMPARE_VERSION | InstallStrategy.SKIP_DEX_OPT;
        if (info.fastOpen) {
            flags |= InstallStrategy.DEPEND_SYSTEM_IF_EXIST;
        }
        return VirtualCore.get().installPackage(info.path, flags);*/
        return null
    }

    override fun removeVirtualApp(packageName: String, userId: Int): Boolean {
        MServiceLoader.getService(IMultiApi::class).uninstallPackage(userId, packageName)
        return true
    }

    private val COLLATOR: Collator = Collator.getInstance(Locale.CHINA)
    private val SCAN_PATH_LIST: List<String> = mutableListOf(
        ".",
        "wandoujia/app",
        "tencent/tassistant/apk",
        "BaiduAsa9103056",
        "360Download",
        "pp/downloader",
        "pp/downloader/apk",
        "pp/downloader/silent/apk"
    )

    private fun isSystemApplication(packageInfo: PackageInfo): Boolean {
        return ((packageInfo.applicationInfo!!.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                && !GmsSupport.isGmsFamilyPackage(packageInfo.packageName))
    }

    fun getDeviceAppList(): List<AppInfo> {
        if (cacheDeviceAppList == null || cacheDeviceAppList!!.size <= 1) {
            cacheDeviceAppList = MServiceLoader.getService(IMultiApi::class).deviceAppList().map {
                AppInfo().apply {
                    packageName = it.pkgInfo?.packageName
                    name = it.title
                    alphabetic = it.alphabetic
                    icon = it.getImageModel()?.loadIcon(Global.appContext.packageManager)
                }
            }
        }
        return cacheDeviceAppList!!
    }

    fun getAvaliableSpace(packageName: String): Int {
        var spaceId = 1
        while (spaceId < Int.MAX_VALUE) {
            val app =
                cacheList?.find { (it is MultiAppData) && it.appInfo.packageName == packageName && it.userId == spaceId }
            if (app == null) {
                break
            } else {
                spaceId++
                continue
            }
        }
        return spaceId
    }

    fun saveConfig(list: List<AppData>?, isPrivacySpace:Boolean = false) {
        if (list == null) return
        var configList: Array<AppConfigData> = arrayOf()
        list?.forEachIndexed { index, appData ->
            if (appData is MultiAppData) {
                configList += (AppConfigData().apply {
                    pkgName = appData.appInfo.packageName
                    spaceId = appData.userId
                    nickName = appData.nickName
                    position = index
                })
            }
        }
        var key = Constant.Key.KEY_APP_LIST_CONFIG
         if (isPrivacySpace) {
            key = Constant.Key.KEY_APP_LIST_CONFIG_PRIVACY_SPACE
        }
        Global.kv().encode(key, Gson().toJson(configList))
    }

    private fun loadHotApps(context: Context) {
        try {
            val hotApps = mutableSetOf<String>()

            // 读取top_app.json
            context.assets?.open("top_app.json")?.use { inputStream ->
                val reader = BufferedReader(InputStreamReader(inputStream))
                val jsonString = reader.readText()
                val jsonArray = JSONArray(jsonString)
                for (i in 0 until jsonArray.length()) {
                    val packageName = jsonArray.getString(i)
                    hotApps.add(packageName)
                }
            }

            // 读取top_game.json
            context.assets?.open("top_game.json")?.use { inputStream ->
                val reader = BufferedReader(InputStreamReader(inputStream))
                val jsonString = reader.readText()
                val jsonArray = JSONArray(jsonString)
                for (i in 0 until jsonArray.length()) {
                    val packageName = jsonArray.getString(i)
                    hotApps.add(packageName)
                }
            }
            hotAppSet.addAll(hotApps)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
