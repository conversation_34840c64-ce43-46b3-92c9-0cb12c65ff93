package com.dong.multiui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.dong.common.data.SpaceEnvInfo
import com.dong.multiui.R
import com.dong.router.MServiceLoader
import com.dong.router.api.IMultiApi

class SpaceInfoFragment : Fragment() {

    private lateinit var deviceInfoContainer: LinearLayout
    private lateinit var modifyButton: Button
    private lateinit var resetButton: Button
    private lateinit var backBtn: View

    private var pkg: String? = null
    private var space: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            pkg = it.getString("pkg")
            space = it.getInt("space")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_device_info, container, false)
        deviceInfoContainer = view.findViewById(R.id.device_info_container)
        modifyButton = view.findViewById(R.id.modify_button)
        resetButton = view.findViewById(R.id.reset_button)
        backBtn = view.findViewById(R.id.back_btn)
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadDeviceInfo()

        modifyButton.setOnClickListener {
            Toast.makeText(requireContext(), "Modify clicked", Toast.LENGTH_SHORT).show()
        }

        resetButton.setOnClickListener {
            Toast.makeText(requireContext(), "Reset clicked", Toast.LENGTH_SHORT).show()
        }

        backBtn.setOnClickListener {
            activity?.finish()
        }
    }

    private fun loadDeviceInfo() {
        if (pkg == null) {
            addInfoItem("Error", "Package name is missing.")
            return
        }

        val multiApi = MServiceLoader.getService(IMultiApi::class)
        val deviceInfo = multiApi.getDeviceInfo(pkg!!, space)

        if (deviceInfo != null) {
            displayDeviceInfo(deviceInfo)
        } else {
            addInfoItem("Error", "Failed to load device info.")
        }
    }

    private fun displayDeviceInfo(deviceInfo: SpaceEnvInfo) {
        deviceInfoContainer.removeAllViews()
        addInfoItem("Device ID", deviceInfo.deviceId)
        addInfoItem("Android ID", deviceInfo.androidId)
        addInfoItem("ICC Serial", deviceInfo.iccSerial)
        addInfoItem("Serial", deviceInfo.serial)
        addInfoItem("Wi-Fi MAC", deviceInfo.wifiMac)
        addInfoItem("Bluetooth MAC", deviceInfo.btMac)
    }

    private fun addInfoItem(key: String, value: String) {
        val textView = TextView(requireContext()).apply {
            text = "$key: $value"
            textSize = 16f
            setPadding(0, 8, 0, 8)
        }
        deviceInfoContainer.addView(textView)
    }

    companion object {
        @JvmStatic
        fun newInstance(pkg: String, space: Int) =
            SpaceInfoFragment().apply {
                arguments = Bundle().apply {
                    putString("pkg", pkg)
                    putInt("space", space)
                }
            }
    }
}