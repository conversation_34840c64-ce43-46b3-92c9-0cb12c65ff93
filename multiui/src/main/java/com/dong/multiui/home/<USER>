package com.dong.multiui.home

import GridSpacingItemDecoration
import SecurityUtil
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Point
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.dong.common.data.Constant
import com.dong.common.data.FlowEventBus
import com.dong.common.data.Global
import com.dong.common.data.ScreenManager
import com.dong.common.data.dp
import com.dong.common.data.events.AppLaunchSuccessEvent
import com.dong.multiui.R
import com.dong.multiui.about.SettingActivity
import com.dong.multiui.databinding.ActivityMultiHomeBinding
import com.dong.multiui.home.HomeContract.HomePresenter
import com.dong.multiui.home.HomeContract.HomeView
import com.dong.multiui.home.adapters.LaunchpadAdapter
import com.dong.multiui.home.models.AddAppButton
import com.dong.multiui.home.models.AppData
import com.dong.multiui.home.models.EmptyAppData
import com.dong.multiui.home.models.MultiAppData
import com.dong.multiui.home.models.PrivacySpaceButton
import com.dong.multiui.home.platform.ReviewHelper
import com.dong.multiui.home.repo.MultiAppRepository
import com.dong.multiui.lock.LockCheckingActivity
import com.dong.multiui.lock.LockSettingActivity
import com.dong.multiui.util.EdgeToEdgeHelper
import com.dong.multiui.util.ViewHelper.clearStorage
import com.dong.multiui.util.ViewHelper.createShortcut
import com.dong.multiui.util.ViewHelper.deleteApp
import com.dong.multiui.util.ViewHelper.forceStop
import com.dong.multiui.util.ViewHelper.renameApp
import com.dong.multiui.widgets.ProgressView
import com.dong.router.MServiceLoader.getService
import com.dong.router.api.IPointData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MultiHomeActivity : AppCompatActivity(), HomeView {

    private val TAG = "dongj"

    private lateinit var binding: ActivityMultiHomeBinding
    private lateinit var mPresenter: HomePresenter
    private lateinit var mLoadingView: ProgressView
    private lateinit var mLauncherView: RecyclerView
    private lateinit var mLaunchpadAdapter: LaunchpadAdapter
    private lateinit var splashContainer: View
    private var itemDecoration: GridSpacingItemDecoration? = null

    private lateinit var actionPopupManager: ActionPopupManager
    private lateinit var launcherDragCallback: LauncherDragCallback
    private lateinit var homeMenuManager: HomeMenuManager
    private val reviewHelper: ReviewHelper = ReviewHelper()

    private var appLaunchSuccessCount = 0
    private var tempShortcutPkg = ""
    private var tempShortcutUserId = -1
    private val pointData: IPointData by lazy {
        getService(IPointData::class)
    }

    val packageInstallReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val packageName = intent.data?.schemeSpecificPart ?: return
            when (action) {
                Intent.ACTION_PACKAGE_ADDED -> {
                    Log.d(TAG, "ACTION_PACKAGE_ADDED: $packageName")
                    MultiAppRepository.cacheDeviceAppList = null
                }

                Intent.ACTION_PACKAGE_REPLACED -> {
                    Log.d(TAG, "ACTION_PACKAGE_REPLACED: $packageName")
                    MultiAppRepository.cacheList = null
                    mPresenter.dataChanged()
                }

                Intent.ACTION_PACKAGE_REMOVED -> {
                    Log.d(TAG, "ACTION_PACKAGE_REMOVED: $packageName")
                    MultiAppRepository.cacheDeviceAppList = null
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = ActivityMultiHomeBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        initView()
        initData()
        handleIntent(intent)
        registerReceiver()
        EdgeToEdgeHelper.updateStatusBar(binding.statusBar)
        ViewCompat.setOnApplyWindowInsetsListener(binding.homeLauncher) { v, insets ->
            val bars = insets.getInsets(WindowInsetsCompat.Type.navigationBars())
            splashContainer.updatePadding(bottom = bars.bottom)
            calculateSpacing(bars.bottom)
            mLauncherView.invalidateItemDecorations()
            insets
        }
        pointData.addEvent(Constant.Point.POINT_KEY_COLD_START, null)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        pointData.addEvent(Constant.Point.POINT_KEY_ON_LOW_MEMORY, null)
        MultiAppRepository.cacheDeviceAppList = null
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(packageInstallReceiver)
        MultiAppRepository.cacheDeviceAppList = null
    }

    private fun registerReceiver() {
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_PACKAGE_ADDED)
            addAction(Intent.ACTION_PACKAGE_REPLACED)
            addAction(Intent.ACTION_PACKAGE_REMOVED)
            addDataScheme("package")
        }
        registerReceiver(packageInstallReceiver, filter)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    override fun onResume() {
        super.onResume()
        requestForReview()
    }

    private fun requestForReview() {
        val current = System.currentTimeMillis()
        val last = Global.kv().getLong(Constant.Key.KEY_APP_REVIEW_TIME, 0)
        val canShow = if (current - last > 1000 * 60 * 60 * 24) {
            true
        } else {
            false
        }
        Log.d(
            "dongj",
            "requestForReview appLaunchSuccessCount($appLaunchSuccessCount) canShow($canShow)"
        )
        if (appLaunchSuccessCount == 1 && canShow) {
            reviewHelper.showReviewDialog(this)
            Global.kv().putLong(Constant.Key.KEY_APP_REVIEW_TIME, current)
        }
    }

    private fun handleIntent(intent: Intent?) {
        if (intent != null && intent.action == "$packageName.SHORTCUT") {
            val pkg = intent.getStringExtra(Intent.EXTRA_PACKAGE_NAME)
            val userId = intent.getIntExtra(Intent.EXTRA_USER, -1)
            if (pkg != null && userId != -1) {
                intent.replaceExtras(null)
                // 跳转到LoadingActivity启动应用
                if (userId == 0) {
                    tempShortcutPkg = pkg
                    tempShortcutUserId = userId
                    Intent(this@MultiHomeActivity, LockCheckingActivity::class.java).apply {
                        startActivityForResult(
                            this,
                            Constant.ActivityRequest.REQUEST_CODE_PASSWORD_CHECK_LAUNCH_SHORTCUT
                        )
                    }
                } else {
                    launchShortcut(pkg, userId)
                }
            }
        }
    }

    private fun launchShortcut(pkg: String?, userId: Int) {
        lifecycleScope.launch(Global.singleExecutor.asCoroutineDispatcher()) {
            val appList = MultiAppRepository.getVirtualApps(userId == 0)
            val app = appList.find {
                it is MultiAppData && it.appInfo.packageName == pkg && it.userId == userId
            }
            pointData.addEvent(
                Constant.Point.POINT_KEY_LAUNCH_APP_SHORTCUT,
                Bundle().apply {
                    if (app is MultiAppData) {
                        putString(Constant.Point.POINT_KEY_PACKAGE_NAME, app.appInfo.packageName)
                        putString(Constant.Point.POINT_KEY_SPACE_ID, app.userId.toString())
                    }
                }
            )
            if (app != null) {
                mPresenter.launchApp(app)
            } else {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@MultiHomeActivity, "The shortcut is invalid.", Toast.LENGTH_SHORT)
                        .show()
                }
            }
        }
    }

    fun initView() {
        bindViews()
        initLaunchpad()
        initActionPopup()
        setupEventListeners()
    }

    fun initData() {
        reviewHelper.init(this)
        HomePresenterImpl(this, false).start()
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            Global.kv().putString(Constant.Key.KEY_APP_VERSION, packageInfo.versionName)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun bindViews() {
        splashContainer = findViewById(R.id.splashContainer)
        mLoadingView = findViewById(R.id.pb_loading_app)
        mLauncherView = findViewById(R.id.home_launcher)
        findViewById<View>(R.id.home_menu).setOnClickListener {
            //homeMenuManager.show(it)
            SettingActivity.start(context)
        }
    }

    private fun initLaunchpad() {
        mLaunchpadAdapter = LaunchpadAdapter(this)
        mLauncherView.adapter = mLaunchpadAdapter
        calculateSpacing()
        initDragCallback()
        val touchHelper = ItemTouchHelper(launcherDragCallback)
        mLauncherView.setTag(R.id.item_touch_helper, touchHelper)
        touchHelper.attachToRecyclerView(mLauncherView)

        mLaunchpadAdapter.setAppClickListener(object : LaunchpadAdapter.OnAppClickListener {
            override fun onAppClick(position: Int, data: AppData?) {
                if (data?.isLoading == false) {
                    if (data is AddAppButton) {
                        onAddAppButtonClick()
                    } else if (data is PrivacySpaceButton) {
                        if (SecurityUtil.lockState == SecurityUtil.LOCK_STATE_NONE) {
                            Intent(this@MultiHomeActivity, LockSettingActivity::class.java).apply {
                                startActivity(this)
                            }
                        } else {
                            Intent(this@MultiHomeActivity, LockCheckingActivity::class.java).apply {
                                startActivityForResult(
                                    this,
                                    Constant.ActivityRequest.REQUEST_CODE_PASSWORD_CHECK
                                )
                            }
                        }
                        pointData.addEvent(
                            Constant.Point.POINT_KEY_LAUNCH_SECRET_ZONE,null
                        )
                    } else {
                        mLaunchpadAdapter.notifyItemChanged(position)
                        mPresenter.launchApp(data)
                    }

                }
            }

            override fun onAppLongClick(position: Int, model: AppData, view: View) {
                if (model is AddAppButton || model is PrivacySpaceButton) return
                showActionPopup(position, model, view)
            }
        })
    }

    private fun initActionPopup() {
        actionPopupManager = ActionPopupManager(this)
    }

    private fun initDragCallback() {
        launcherDragCallback = LauncherDragCallback(
            adapter = mLaunchpadAdapter,
            onActionPopupDismiss = { actionPopupManager.dismiss() }
        )
    }

    private fun calculateSpacing(bottomSpace: Int = 25.dp) {
        if (itemDecoration != null) {
            mLauncherView.removeItemDecoration(itemDecoration!!)
        }
        var spanCount = 3
        if (ScreenManager.isLargeWidth()) {
            spanCount = 6
        } else if (ScreenManager.isMediumWidth()) {
            spanCount = 5
        }
        val layoutManager = GridLayoutManager(this, spanCount)
        mLauncherView.layoutManager = layoutManager
        val spacingPx = calculateSpacing(this, spanCount, 110.dp)
        itemDecoration = GridSpacingItemDecoration(spanCount, spacingPx, bottomSpace)
        mLauncherView.addItemDecoration(itemDecoration!!)
    }

    private fun calculateSpacing(activity: Activity, spanCount: Int, itemWidthPx: Int): Int {
        val screenWidthPx = getUsableScreenSize(activity).x
        val totalItemsWidth = spanCount * itemWidthPx
        val remainingSpace = screenWidthPx - totalItemsWidth - 32.dp
        return remainingSpace / (spanCount - 1)
    }

    fun getUsableScreenSize(activity: Activity): Point {
        val size = Point()
        activity.windowManager.defaultDisplay.getSize(size)
        return size
    }

    private fun showActionPopup(position: Int, data: AppData, anchorView: View) {
        actionPopupManager.show(
            position = position,
            anchorView = anchorView,
            onDeleteClick = { deleteApp(this, position, mLaunchpadAdapter, mPresenter) },
            onShortcutClick = { createShortcut(position, mLaunchpadAdapter, mPresenter) },
            onClearStorageClick = { clearStorage(this, position, mLaunchpadAdapter, mPresenter) },
            onForceStopClick = { forceStop(this, position, mLaunchpadAdapter, mPresenter) },
            onRenameClick = {
                renameApp(
                    this,
                    position,
                    mLaunchpadAdapter,
                    mPresenter,
                    { MultiAppRepository.saveConfig(mLaunchpadAdapter.getList(),false) })
            },
            onModifySpaceInfoClick = {
                val appData = mLaunchpadAdapter.getList()?.get(position) as? MultiAppData
                if (appData != null) {
                    val intent = Intent(this, ContainerActivity::class.java)
                    intent.putExtra("fragmentName", "SpaceInfoFragment")
                    intent.putExtra("pkg", appData.appInfo.packageName)
                    intent.putExtra("space", appData.userId)
                    startActivity(intent)
                }
            }
        )
    }


    private fun onAddAppButtonClick() {
        gotoAddDeviceApp()
    }

    private fun gotoAddDeviceApp() {
        val intent = Intent(this, ContainerActivity::class.java)
        intent.putExtra("fragmentName", "DeviceAppFragment")
        startActivityForResult(intent, Constant.ActivityRequest.REQUEST_CODE_QUERY_PACKAGES)
        pointData.addEvent(Constant.Point.POINT_KEY_GO_TO_ADD_APP, null)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        ScreenManager.update(this.applicationContext)
        calculateSpacing()
        mLauncherView.invalidateItemDecorations()
    }

    override fun onStop() {
        super.onStop()
        MultiAppRepository.saveConfig(mLaunchpadAdapter.getList(),false)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            Constant.ActivityRequest.REQUEST_CODE_QUERY_PACKAGES -> {
                if (resultCode == RESULT_OK && data != null) {
                    MultiAppRepository.cacheList = null
                    mPresenter.dataChanged()
                }
            }

            Constant.ActivityRequest.REQUEST_CODE_PASSWORD_CHECK -> {
                if (resultCode == RESULT_OK) {
                    Intent(this, ContainerActivity::class.java).apply {
                        putExtra("fragmentName", "PrivacySpaceFragment")
                        startActivity(this)
                    }
                }
            }

            Constant.ActivityRequest.REQUEST_CODE_PASSWORD_CHECK_LAUNCH_SHORTCUT -> {
                if (resultCode == RESULT_OK) {
                    if (tempShortcutPkg.isNotEmpty() && tempShortcutUserId != -1)
                        launchShortcut(tempShortcutPkg, tempShortcutUserId)
                }
                tempShortcutPkg = ""
                tempShortcutUserId = -1
            }
        }
    }

    // HomeView implementation
    override fun getActivity(): Activity = this
    override fun getContext() = this
    override fun setPresenter(presenter: HomePresenter?) {
        mPresenter = presenter!!
    }

    override fun showLoading() {
        mLoadingView.visibility = View.VISIBLE
        mLoadingView.startAnim()
    }

    override fun hideLoading() {
        mLoadingView.visibility = View.GONE
        mLoadingView.stopAnim()
    }

    override fun loadFinish(appModels: ArrayList<AppData>?) {
        pointData.addEvent(
            Constant.Point.POINT_KEY_INSTALL_APP_COUNT,
            Bundle().apply {
                putInt(Constant.Point.POINT_KEY_INSTALL_APP_COUNT, appModels?.size ?: 0)
            }
        )
        appModels?.add(0, PrivacySpaceButton(this))
        appModels?.add(AddAppButton(this))
        mLaunchpadAdapter.setList(appModels)
        hideLoading()
        hideSplashView()

    }

    private fun hideSplashView() {
        splashContainer.visibility = View.GONE
    }

    override fun loadError(err: Throwable?) {
        err?.printStackTrace()
        hideLoading()
    }

    override fun showGuide() {
        // Implement guide functionality
    }

    override fun addAppToLauncher(model: AppData?) {
        val dataList = mLaunchpadAdapter.getList()!!
        var replaced = false
        for (i in dataList.indices) {
            val data = dataList[i]
            if (data is EmptyAppData) {
                mLaunchpadAdapter.replace(i, model!!)
                replaced = true
                break
            }
        }
        if (!replaced) {
            mLaunchpadAdapter.add(model!!)
            mLauncherView.smoothScrollToPosition(mLaunchpadAdapter.itemCount - 1)
        }
    }

    override fun removeAppToLauncher(model: AppData?) {
        mLaunchpadAdapter.remove(model!!)
        MultiAppRepository.saveConfig(mLaunchpadAdapter.getList(),false)
    }

    override fun refreshLauncherItem(model: AppData?) {
        mLaunchpadAdapter.refresh(model!!)
    }

    override fun askInstallGms() {
        AlertDialog.Builder(this)
            .setTitle("Hi")
            .setMessage("We found that your device has been installed the Google service, whether you need to install them?")
            .setPositiveButton(android.R.string.ok, null)
            .setNegativeButton(android.R.string.cancel) { _, _ ->
                Toast.makeText(
                    this@MultiHomeActivity,
                    "You can also find it in the Settings~",
                    Toast.LENGTH_LONG
                ).show()
            }
            .setCancelable(false)
            .show()
    }

    private fun setupEventListeners() {
        // 订阅应用启动成功事件
        FlowEventBus.observe<AppLaunchSuccessEvent>()
            .onEach { event ->
                appLaunchSuccessCount++
                // 可以在这里更新UI或执行其他操作
                updateAppLaunchStatus(event)
            }
            .launchIn(lifecycleScope)
    }

    private fun updateAppLaunchStatus(event: AppLaunchSuccessEvent) {

    }

    companion object {
        fun goHome(context: Context) {
            val intent = Intent(context, MultiHomeActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }
    }
}
