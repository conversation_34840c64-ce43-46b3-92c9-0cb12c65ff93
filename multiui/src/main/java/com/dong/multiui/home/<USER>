package com.dong.multiui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.motionEventSpy
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import com.dong.common.data.SpaceEnvInfo
import com.dong.multiui.R
import com.dong.router.MServiceLoader
import com.dong.router.api.IMultiApi

class SpaceInfoComposeFragment : Fragment() {

    private var pkg: String? = null
    private var space: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            pkg = it.getString("pkg")
            space = it.getInt("space")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                SpaceInfoScreen(
                    pkg = pkg,
                    space = space,
                    onBackClick = { activity?.finish() },
                )
            }
        }
    }

    companion object {
        @JvmStatic
        fun newInstance(pkg: String, space: Int) =
            SpaceInfoComposeFragment().apply {
                arguments = Bundle().apply {
                    putString("pkg", pkg)
                    putInt("space", space)
                }
            }
    }
}

@Preview
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpaceInfoScreen(
    pkg: String? = null,
    space: Int = 0,
    onBackClick: () -> Unit = {},
) {
    var deviceInfo by remember { mutableStateOf<SpaceEnvInfo?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Load device info
    LaunchedEffect(pkg, space) {
        if (pkg == null) {
            errorMessage = "Package name is missing."
            isLoading = false
            return@LaunchedEffect
        }

        try {
            val multiApi = MServiceLoader.getService(IMultiApi::class)
            val info = multiApi.getDeviceInfo(pkg, space)
            if (info != null) {
                deviceInfo = info
            } else {
                errorMessage = "Failed to load device info."
            }
        } catch (e: Exception) {
            errorMessage = "Error: ${e.message}"
        } finally {
            isLoading = false
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // Top bar
        TopAppBar(
            title = {
                Text(
                    text = "Space Info",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF27272A)
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        painter = painterResource(R.drawable.multi_icon_back),
                        contentDescription = "Back",
                        tint = Color(0xFF27272A)
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.White
            )
        )

        // Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.multi_bg_full_screen), // 替换为你的图片资源ID
                contentDescription = "Background Image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }

                errorMessage != null -> {
                    Text(
                        text = errorMessage!!,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp),
                        color = Color.Red
                    )
                }

                deviceInfo != null -> {
                    DeviceInfoContent(space, deviceInfo = deviceInfo!!)
                }
            }
        }
    }
}

@Composable
fun DeviceInfoContent(space: Int, deviceInfo: SpaceEnvInfo) {
    val infoItems = listOf(
        "Device ID" to deviceInfo.deviceId,
        "Android ID" to deviceInfo.androidId,
        "Serial" to deviceInfo.serial,
        "Wi-Fi MAC" to deviceInfo.wifiMac,
        "Bluetooth MAC" to deviceInfo.btMac
    )
    Column(modifier = Modifier.fillMaxSize().padding(horizontal = 16.dp, vertical = 16.dp)) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ){
            DeviceInfoItem(key = "Space ID", value = space.toString())
        }
        Spacer(modifier = Modifier.height(16.dp))
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            LazyColumn(
                modifier = Modifier
                    .wrapContentSize(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(infoItems) { (key, value) ->
                    DeviceInfoItem(key = key, value = value)
                    HorizontalDivider( modifier = Modifier.padding(horizontal = 16.dp).height(1.dp), color = Color(0xFFEBEBF0))
                }
            }
        }
        Spacer(modifier = Modifier.weight(1f))
        // Bottom buttons
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentSize()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            val gradientColors = listOf(Color(0xFF43A8F0),Color(0xFFA67AFF))

            Button(
                onClick = { },
                modifier = Modifier.weight(1f).height(40.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent),
                contentPadding =PaddingValues(),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .shadow(elevation = 1.dp)
                        .background(Brush.linearGradient(gradientColors)), // 线性渐变
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Modify",
                        color = Color.White,
                    )
                }
            }

            Button(
                onClick = { },
                modifier = Modifier.weight(1f).height(40.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.White),
                contentPadding =PaddingValues(),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize().shadow(elevation = 1.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Reset",
                        color = Color.Black,
                    )
                }
            }
        }
    }


}

@Composable
fun DeviceInfoItem(key: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = key,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            color = Color(0xFF6B7280)
        )
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            color = Color(0xFF28282B)
        )
    }

}
