package com.dong.multiui.home

import android.os.Bundle
import android.view.Window
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import com.dong.multiui.R

class ContainerActivity:AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        val fragmentName = intent.getStringExtra("fragmentName")
        val fragment = when(fragmentName) {
            "DeviceAppFragment" -> DeviceAppFragment()
            "PrivacySpaceFragment" -> PrivacySpaceFragment()
            //"SpaceInfoFragment" -> SpaceInfoFragment()
            "SpaceInfoFragment" -> SpaceInfoComposeFragment()
            else -> null
        }
        if(fragment == null) {
            finish()
            return
        }
        fragment.arguments = intent.extras
        overridePendingTransition(R.anim.slide_right_to_middle, R.anim.slide_middle_to_left)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.multi_activity_container)
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.slide_left_to_middle, R.anim.slide_middle_to_right)
    }
}