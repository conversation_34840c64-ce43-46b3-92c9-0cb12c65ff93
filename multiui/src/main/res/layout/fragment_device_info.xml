<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:background="#fff"
        android:layout_height="25dp"/>

    <RelativeLayout
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#fff">

        <com.dong.multiui.widgets.ClickableFrameLayout
            android:id="@+id/back_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            app:clickEffectColor="@color/color_gray"
            app:clickEffectType="color"
            app:rippleRadius="30dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@drawable/multi_icon_back" />

        </com.dong.multiui.widgets.ClickableFrameLayout>


        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@+id/back_btn"
            android:lineHeight="27dp"
            android:text="Space Info"
            android:textColor="#27272A"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/device_info_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp" />
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/modify_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Modify" />

        <Button
            android:id="@+id/reset_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="Reset" />
    </LinearLayout>
</LinearLayout>