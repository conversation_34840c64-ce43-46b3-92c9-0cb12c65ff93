package com.dong.multirun.app.manager

import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import com.bumptech.virtual.VirtualApi
import com.dong.common.data.AppSpaceData
import com.dong.common.data.DeviceAppData
import com.dong.multirun.app.ui.item.AppItem
import com.dong.multirun.app.virtual.Favorites
import com.dong.multirun.mobile.AppRuntime
import com.dong.multirun.mobile.executor.AppExecutor
import com.dong.multirun.mobile.utils.AlphabeticIndexCompat
import java.io.File

object AppManager {

    var mIndexCompat: AlphabeticIndexCompat = AlphabeticIndexCompat(AppRuntime.currentApp())

    fun allApplist(): List<AppSpaceData> {
        val list = ArrayList<AppSpaceData>()
        val userIds = VirtualApi.getAllUserSpace()
        if (userIds != null) {
            for (id in userIds) {
                var packages = VirtualApi.getInstalledPackageNames(
                    PackageManager.MATCH_UNINSTALLED_PACKAGES,
                    id
                )
                if (packages == null) {
                    packages = emptyList()
                }
                for (pkg in packages) {
                    val appData = AppSpaceData()
                    var info = VirtualApi.getPackageInfo(pkg, 0, id)
                    var valid = info != null && File(info.applicationInfo.sourceDir).exists()
                    if (!valid) {
                        // try fix
                        VirtualApi.installPackageFromHost(id, pkg, true)
                        info = VirtualApi.getPackageInfo(pkg, 0, id)
                        valid = info != null && File(info.applicationInfo.sourceDir).exists()
                    }
                    if (valid) {
                        appData.pkgInfo = info
                    }
                    val clone = VirtualApi.isCloneMode(id, pkg)
                    var frozen = info == null || !File(info.applicationInfo.sourceDir).exists()
                    appData.spaceId = id
                    appData.clone = clone
                    appData.frozen = frozen
                    list.add(appData)
                }

            }
        }
        return list
    }

    fun deviceAppList(): List<DeviceAppData> {
        val list = ArrayList<DeviceAppData>()
        val pm = AppRuntime.currentApp().packageManager
        val hostAbi = AppItem.abi(AppRuntime.getPackageInfo())
        var pkgs: Collection<PackageInfo>
        pkgs = try {
            pm.getInstalledPackages(PackageManager.GET_META_DATA)
        } catch (e: Exception) {
            emptyList()
        }
        for (packageInfo in pkgs) {
            if (packageInfo.versionName != null && packageInfo.versionName.contains(Build.VERSION.INCREMENTAL)) {
                continue
            }
            if (packageInfo.packageName == AppRuntime.getPackageName()) {
                continue
            }
            if (!Favorites.isGoogleSuite(packageInfo.packageName) && (packageInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0) {
                continue
            }
            if (Favorites.isDisable(packageInfo)) {
                continue
            }
            //            if (needPass(packageInfo.packageName)) {
//                continue;
//            }
            val item = DeviceAppData(packageInfo)
            if (hostAbi != null) {
                val abi = AppItem.abi(packageInfo)
                // abi 不一致先忽略
                if (hostAbi != abi && packageInfo.packageName != "com.android.chrome") {
                    continue
                }
            }
            try {
                val alpha: String = mIndexCompat.computeSectionName(item.title)
                item.alphabetic = alpha
            } catch (e: Throwable) {
                // ignore
                item.alphabetic = "#"
            }

            list.add(item)
        }
        return list

    }
}