import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import org.gradle.kotlin.dsl.implementation


plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsKotlinAndroid)
    //alias(libs.plugins.firebaseCrashlytics)
    alias(libs.plugins.googleService)
    id("MatrixProguard")
    //alias(libs.plugins.ksp)
    //alias(libs.plugins.googleDaggerHilt)
}

configure<com.matrix.build.tools.stringfog.plugin.StringFogExtension> {
    fogPackages = arrayOf("com.dong.multirun")
    mode = com.matrix.build.tools.stringfog.plugin.StringFogMode.bytes
    enable = true
}

android {
    namespace = "com.dong.multirun.app"
    compileSdk = 35


    defaultConfig {
        applicationId = "com.dong.multirun"
        minSdk = 24
        targetSdk = 34
        versionCode = 21
        versionName = "1.0.21"
        testInstrumentationRunner = "com.dong.multirun.app.HiltTestRunner"

        ndk {
            abiFilters.addAll(arrayOf("armeabi-v7a", "arm64-v8a"))
        }
        buildConfigField("boolean", "ENABLE_REQUIRE_ENV", "false")
        buildConfigField("boolean", "ENABLE_PLUGIN", "false")
        buildConfigField("boolean", "ENABLE_FULL_ADS", "false")
    }

    externalNativeBuild {
        cmake {
            path("src/main/cpp/CMakeLists.txt")
        }
    }
    signingConfigs {
        register("aab") {
            keyAlias = "dong"
            keyPassword = "djp.6640426"
            storeFile = file("keys/keystore.jks")
            storePassword = "djp.6640426"
        }
        register("release") {
            initWith(getByName("aab"))
        }
    }

    val dir = "src/main/java/com/dong/multirun/app/ui/"
    sourceSets {
        getByName("main") {
            val root = File(projectDir, dir)
            res.srcDirs(root.listFiles()!!.map {
                it.path + "/res"
            })
            res.srcDirs(
                root.path + "/res"
            )
        }
    }
    flavorDimensions += "channel"
//    flavorDimensions +="ads"
    flavorDimensions +="analytics"
    flavorDimensions += "sign"
    productFlavors {
//        create("telegram").apply {
//            dimension = "channel"
//            versionNameSuffix = ".10"
//            targetSdk = 33
//        }

        create("playstore") {
            dimension = "channel"
            versionNameSuffix = ".20"
            targetSdk = 34
            buildConfigField("boolean", "ENABLE_REQUIRE_ENV", "true")
        }
//        create("admob").apply {
//            dimension = "ads"
//        }
        create("firebase"){
            dimension = "analytics"
        }
//        create("none"){
//            dimension = "analytics"
//        }
        create("aab") {
            dimension = "sign"
            signingConfig = signingConfigs.getByName("aab")
        }
        create("apk") {
            dimension = "sign"
            signingConfig = signingConfigs.getByName("release")
        }
    }
    androidComponents {
        beforeVariants { variantBuilder ->

            variantBuilder.enable = false
            // 非谷歌渠道禁用debug 包 和 aab 签名包

            if (variantBuilder.productFlavors.contains("channel" to "playstore")){
                if (variantBuilder.productFlavors.contains("sign" to "aab")) {
                    if (variantBuilder.buildType == "debug") {
                        return@beforeVariants
                    }
                }
                if (variantBuilder.productFlavors.containsAll(
                        listOf(
//                            "ads" to "admob",
                            "analytics" to "firebase"
                        )
                    )
                ) {
                    variantBuilder.enable = true
                }
            } else if (variantBuilder.buildType == "debug") {
                return@beforeVariants
            } else if (variantBuilder.productFlavors.contains("sign" to "aab")){
                variantBuilder.enable = true
                return@beforeVariants
            }
//            if (variantBuilder.productFlavors.containsAll(listOf("channel" to "rustore", "ads" to "yandex"))) {
//                variantBuilder.enable = true
//            }
////            if (variantBuilder.productFlavors.contains("channel" to "telegram")){
//                variantBuilder.enable = true
//            }
//            if (variantBuilder.productFlavors.contains("channel" to "xposed")) {
//                variantBuilder.enable = true
//            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
//            configure<CrashlyticsExtension> {
//                mappingFileUploadEnabled = false
//                nativeSymbolUploadEnabled = false
//            }
        }
        debug {
            signingConfig = signingConfigs.getByName("release")
        }
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true
        buildConfig = true
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

// KSP configuration for Hilt
//ksp {
//    arg("dagger.hilt.android.internal.disableAndroidSuperclassValidation", "true")
//}

dependencies {
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))
    api(project(mapOf("path" to ":core")))
    implementation(project(":router"))
    implementation(project(":multiui"))
    implementation(project(":common"))
//    implementation("com.google.firebase:firebase-firestore-ktx")
    // 广告
//    "yandexImplementation"(project(":yandex-ads"))
//    "admobImplementation"(project(":ads"))
//    // xposed 独有渠道
//    "xposedImplementation"(project(":xposed"))

    val billing_version = "7.1.1"

//    implementation("com.android.billingclient:billing:$billing_version")
//    implementation("com.android.billingclient:billing-ktx:$billing_version")
    implementation(libs.androidx.preference.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.google.android.material)
    implementation(libs.glide)
    implementation("org.jetbrains.kotlin:kotlin-reflect:2.1.21")

    // Hilt dependencies
    //implementation(libs.hilt.android)
    //ksp(libs.hilt.compiler)

    // Hilt testing dependencies
    androidTestImplementation(libs.hilt.android.testing)

    // Additional Espresso dependencies for better testing
    androidTestImplementation("androidx.test.espresso:espresso-contrib:3.5.1")
    androidTestImplementation("androidx.test.espresso:espresso-intents:3.5.1")
    androidTestImplementation("androidx.test:rules:1.5.0")
    androidTestImplementation("androidx.test:runner:1.5.2")

    // Mockito for testing
    androidTestImplementation("org.mockito:mockito-android:5.1.1")
    androidTestImplementation("org.mockito:mockito-core:5.1.1")

    // Coroutines testing
    androidTestImplementation(libs.kotlinx.coroutines.test)

    // Architecture components testing
    androidTestImplementation("androidx.arch.core:core-testing:2.2.0")
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.test.ext.junit)
    androidTestImplementation(libs.androidx.test.espresso.core)
    androidTestImplementation(libs.androidx.uiautomator)
}